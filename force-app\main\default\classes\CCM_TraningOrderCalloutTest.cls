@IsTest
public class CCM_TraningOrderCalloutTest {
	@IsTest
    static void testOrder(){
        Account objAccount = new Account (Name = 'pwc Test');
        insert objAccount;
        Account_Address__c objAccountAddress = new Account_Address__c();
        objAccountAddress.Country__c = 'DE';
        objAccountAddress.City__c = 'Steinheim';
        objAccountAddress.Address1__c = 'Autenbacherstr. 11';
        insert objAccountAddress;
        Account_Address__c objAccountAddress2 = new Account_Address__c();
        objAccountAddress2.Country__c = 'DE';
        objAccountAddress2.City__c = 'Steinheim';
        objAccountAddress2.Address1__c = 'Autenbacherstr. 121';
        insert objAccountAddress2;
        Address_With_Program__c objAddressProgram = new Address_With_Program__c();
        objAddressProgram.Customer_Line_Oracle_ID__c = '168111';
        objAddressProgram.Account_Address__c = objAccountAddress.Id;
        Sales_Program__c authBrand = new Sales_Program__c();
        authBrand.Customer__c = objAccount.Id;
        authBrand.Status__c = 'Active';
        authBrand.Approval_Status__c = 'Approved';
        insert authBrand;
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Price__c = 100,
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR'
        );
        insert courseArrangement;
        Course_Register__c courseRegister = new Course_Register__c(
            Billing_Address__c = objAccountAddress.Id,
            Course_Arrangement__c = courseArrangement.Id,
            Customer__c = objAccount.Id,
            Payment_Term__c = 'EEG01',
            PCS__c = 2,
            Price_Pcs__c = 100,
            Status__c = 'Draft',
            Training_Course__c = objSetting.Id,
            CurrencyIsoCode = 'EUR',
            Trainee__c = '["Sample Trainee"]'
        );
        insert courseRegister;
        Course_Register_Item__c itemData = new Course_Register_Item__c(Course_Register__c = courseRegister.id);
        insert itemData;
        CCM_TraningOrderCallout.pushRequestInfo(courseRegister.Id);
    }
}