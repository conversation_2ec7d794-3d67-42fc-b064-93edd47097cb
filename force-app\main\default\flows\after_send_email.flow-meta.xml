<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>send_After_Email</name>
        <label>send After Email</label>
        <locationX>308</locationX>
        <locationY>276</locationY>
        <actionName>CCM_RegisterSendAfterEmail</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>lstRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
    </actionCalls>
    <apiVersion>58.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>after send email {!$Flow.CurrentDateTime}</interviewLabel>
    <label>after send email</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Confirmed</stringValue>
            </value>
        </filters>
        <object>Course_Register__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Training_After</name>
            <connector>
                <targetReference>send_After_Email</targetReference>
            </connector>
            <label>Training After</label>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Days</offsetUnit>
            <recordField>Training_End_Time__c</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
