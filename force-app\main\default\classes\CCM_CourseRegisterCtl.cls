/**
 * Honey
 * 注册课程步骤
 */
public without sharing class CCM_CourseRegisterCtl {
    public final static String strRichTextImageUrl = '/servlet/rtaImage';
    public final static String strDomain = Label.CCM_File_Storage_Url;
    //查询可选的Course List
    @AuraEnabled
    public static List<Map<String,Object>> queryCourseSettingList( Decimal pageNumber, Integer pageSize){
        //查询Course信息-->只能看到已发布的课程信息
        try {
            User objUser = [
                SELECT Id,Name,Profile.Name,UserType FROM User WHERE Id = :UserInfo.getUserId()
            ];
            List<Training_Course_Setting__c>  lstTrainingCourse = new List<Training_Course_Setting__c> ();
            if(objUser.UserType != null && objUser.UserType.contains('Partner')){
                lstTrainingCourse = [
                    SELECT Id,Name,Course_Desc__c,Course_Name__c,Course_Name__r.Name
                    FROM Training_Course_Setting__c WHERE Status__c = 'Launched'
                    AND visible_for_Partner__c = TRUE
                ];
            }else{
                lstTrainingCourse = [
                SELECT Id,Name,Course_Desc__c,Course_Name__c,Course_Name__r.Name
                FROM Training_Course_Setting__c WHERE Status__c = 'Launched'
                ];

            }



            List<Map<String,Object>> lstCourseSettingInfo = new List<Map<String,Object>>();
            List<String> lstTrainingcourseId = new List<String>();
            for(Training_Course_Setting__c objCourseSetting : lstTrainingCourse){
                lstTrainingcourseId.add( objCourseSetting.id);
            }
            //通过Training CourseId查询Training Arrangement信息
            List<Course_Arrangement__c> lstCourseArrangement = [
                SELECT Id,Training_Course_Setting__c,Training_Course_Setting__r.Course_Name__r.Name,Training_Course_Setting__r.Name,
                Course_Date__c,End_Time__c,Start_Time__c,Training_Location__c,Total_Slot__c,Register_Slot__c,Status__c,Price__c,Name,
                Training_Location__r.Final_Address__c,Avaliable_Slot__c,
                Training_Course_Setting__r.Course_Desc__c FROM Course_Arrangement__c WHERE Training_Course_Setting__c IN : lstTrainingcourseId
                AND Status__c = 'Launched' AND Course_Date__c > :Date.today()
            ];
            // 遍历Arrangement信息。存放可选Arrangeent数量
            Map<String,Integer> mapSetting2Qty = new Map<String,Integer>();
            for(Course_Arrangement__c objCourseArrangement : lstCourseArrangement){
                Integer Qty = mapSetting2Qty.containsKey(objCourseArrangement.Training_Course_Setting__c) ?
                mapSetting2Qty.get(objCourseArrangement.Training_Course_Setting__c) : 0;
                Qty = Qty +1;
                mapSetting2Qty.put(objCourseArrangement.Training_Course_Setting__c, Qty);
            }
            for(Training_Course_Setting__c objCourseSetting : lstTrainingCourse){
                Map<String,Object> mapFeild2Value = new Map<String,Object>();
                mapFeild2Value.put('no', objCourseSetting.Name);
                mapFeild2Value.put('id', objCourseSetting.id);

                mapFeild2Value.put('courseName', objCourseSetting.Course_Name__r.Name);
                if( mapSetting2Qty.containsKey(objCourseSetting.Id)){
                    //设置QTy
                    mapFeild2Value.put('qty', mapSetting2Qty.get(objCourseSetting.Id));

                }else{
                    //表示不存在Qty
                    mapFeild2Value.put('qty',0);
                }

                String description = objCourseSetting.Course_Desc__c;
                if(String.isNotBlank(description)){
                    description = description.substring(0, description.indexOf('</'));
                    description = description.substring(description.lastIndexOf('>')+1, description.length());
                }


                mapFeild2Value.put('courseDescription', description);
                lstCourseSettingInfo.add(mapFeild2Value);
            }
            if(lstCourseSettingInfo != null && lstCourseSettingInfo.size() > 0 ){
                return getCurrentData(lstCourseSettingInfo,pageNumber,pageSize);
            }else{
                return null;
            }
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    //根据course List查询 行上的Arrangement
    @AuraEnabled
    public static Map<String,Object> queryCourseArrangementList(String courseSettingId){
        system.debug('courseSettingId---->'+courseSettingId);
        User objUser = [
            SELECT Id,Name,Profile.Name,UserType FROM User WHERE Id = :UserInfo.getUserId()
        ];
        try {
            Map<String,Object> mapFeild2Value = new Map<String,Object>();
            String userLanguage = UserInfo.getLanguage();
            List<Course_Arrangement__c> lstCourseArrangement=  new List<Course_Arrangement__c> ();
            lstCourseArrangement = [
                SELECT Id,Training_Course_Setting__c,Training_Course_Setting__r.Course_Name__r.Name,Training_Course_Setting__r.Name,
                Course_Date__c,End_Time__c,Start_Time__c,Training_Location__c,Total_Slot__c,Register_Slot__c,Status__c,Price__c,Name,
                Training_Location__r.Final_Address__c,Avaliable_Slot__c,
                Training_Course_Setting__r.Course_Desc__c FROM Course_Arrangement__c WHERE Training_Course_Setting__c = : courseSettingId
                AND Status__c = 'Launched' AND Course_Date__c > :Date.today()
            ];
            //查询courseSetting信息
            List<Training_Course_Setting__c> lstTrainingSetting= [
                SELECT Id,Name, Course_Name__r.Name,Course_Desc__c FROM Training_Course_Setting__c WHERE Id = : courseSettingId
            ];
            //根据settingId查询关联产品信息
            List<Traing_Course_Product__c> lstCourseProduct = [
                SELECT Id,Course_Arrangement__c,Product__c,Course_Arrangement__r.Training_Course_Setting__c,
                Product__r.Item_Description_DE__c,Product__r.Item_Description_EN__c,Product__r.Name
                FROM Traing_Course_Product__c WHERE Course_Arrangement__r.Training_Course_Setting__c = :courseSettingId
                AND Course_Arrangement__r.Course_Date__c > :Date.today()
            ];
            Map<String,List<Traing_Course_Product__c>> mapArrangementId2Product = new Map<String,List<Traing_Course_Product__c>>();
            if(lstCourseProduct != null  && lstCourseProduct.size()>0){
                for(Traing_Course_Product__c objProduct : lstCourseProduct){
                    List<Traing_Course_Product__c> lstCourseProductInfo = mapArrangementId2Product.containsKey(objProduct.Course_Arrangement__c) ?
                    mapArrangementId2Product.get(objProduct.Course_Arrangement__c)  : new List<Traing_Course_Product__c>();
                    lstCourseProductInfo.add(objProduct);
                    mapArrangementId2Product.put(objProduct.Course_Arrangement__c,lstCourseProductInfo);
                }
            }
            system.debug('mapArrangementId2Product--->'+mapArrangementId2Product);
            if(lstTrainingSetting != null && lstTrainingSetting.size()>0 ){
                mapFeild2Value.put('courseNo', lstTrainingSetting[0].Name);
                mapFeild2Value.put('courseId', lstTrainingSetting[0].Id);
                mapFeild2Value.put('courseName', lstTrainingSetting[0].Course_Name__r.Name);
                String strDesc = lstTrainingSetting[0].Course_Desc__c;
                if(objUser.UserType.contains('Partner')){
                    //表示是protal端
                    if (String.isNotBlank(strDesc) && strDesc.contains(strRichTextImageUrl)) {
                        strDesc = strDesc.replace(strRichTextImageUrl, Label.CCM_File_Storage_Url + strRichTextImageUrl);
                        strDesc = strDesc.replace('amp;','');
                        strDesc = strDesc.replace('/;','');
                    }
                    system.debug('protal端-----》');
                }
                system.debug('objUser-----》'+objUser);
                system.debug('strDesc-----》'+strDesc);

                mapFeild2Value.put('courseDescription', strDesc);
            }
            if(lstCourseArrangement != null && lstCourseArrangement.size()>0){


                //存入Arrangement信息
                List<Map<String,Object>> lstArrangemtInfo = new List<Map<String,Object>>();
                for(Course_Arrangement__c objCourse : lstCourseArrangement){
                    Map<String,Object> mapArrangementInfo = new Map<String,Object>();
                    mapArrangementInfo.put('arrangementId', objCourse.Id);
                    mapArrangementInfo.put('arrangementNo', objCourse.Name);
                    mapArrangementInfo.put('arrangementStartTime', objCourse.Start_Time__c ==  null ? null : DateTime.newInstance(Date.today(), objCourse.Start_Time__c).format('HH:mm'));
                    mapArrangementInfo.put('arrangementendTime', objCourse.End_Time__c ==  null ? null :  DateTime.newInstance(Date.today(), objCourse.End_Time__c).format('HH:mm'));
                    mapArrangementInfo.put('arrangementDate', objCourse.Course_Date__c);
                    mapArrangementInfo.put('arrangementLocation', objCourse.Training_Location__r.Final_Address__c);
                    mapArrangementInfo.put('arrangementprice', objCourse.Price__c);
                    mapArrangementInfo.put('arrangementSlot', objCourse.Avaliable_Slot__c+ '/'+  objCourse.Total_Slot__c.setScale(0,RoundingMode.HALF_UP) );
                    List<Traing_Course_Product__c> lstCourseProductInfo = mapArrangementId2Product.get(objCourse.Id);
                    List<Map<String,Object>> lstProductInfo = new List<Map<String,Object>>();
                    if(lstCourseProductInfo != null && lstCourseProductInfo.size() > 0 ){
                        for(Traing_Course_Product__c objTrainingProduct : lstCourseProductInfo){
                            Map<String,Object> mapProductInfo = new Map<String,Object>();
                            mapProductInfo.put('productName', objTrainingProduct.Product__r.Name);
                            if(userLanguage == CCM_Constants.DE){
                                mapProductInfo.put('productDescription', objTrainingProduct.Product__r.Item_Description_DE__c);
                            }else{
                                mapProductInfo.put('productDescription', objTrainingProduct.Product__r.Item_Description_EN__c);
                            }
                            lstProductInfo.add(mapProductInfo);
                        }
                        mapArrangementInfo.put('lstProductInfo',lstProductInfo);
                    }

                    lstArrangemtInfo.add(mapArrangementInfo);

                }
                //通过setting查询Propose 信息
                List<Propose_Course_Arrangement__c> lstPropose = new List<Propose_Course_Arrangement__c>();
                lstPropose = [
                    SELECT Id,Course_Date__c,End_Time__c,Start_Time__c,Training_Location__c,Training_Location__r.Final_Address__c,Description__c
                    FROM Propose_Course_Arrangement__c WHERE Training_Course_Setting__c = : courseSettingId
                    AND CreatedById = :UserInfo.getUserId()
                ];
                List<Map<String,Object>> lstProposeArrangement = new List<Map<String,Object>>();
                for(Propose_Course_Arrangement__c objPropose : lstPropose){
                    Map<String,Object> mapProposeArrangement = new Map<String,Object>();
                    mapProposeArrangement.put('proposeId', objPropose.Id);
                    mapProposeArrangement.put('proposeStartTime', objPropose.Start_Time__c ==  null ? null : DateTime.newInstance(Date.today(), objPropose.Start_Time__c).format('HH:mm'));
                    mapProposeArrangement.put('proposeEndTime', objPropose.End_Time__c ==  null ? null :  DateTime.newInstance(Date.today(), objPropose.End_Time__c).format('HH:mm'));
                    mapProposeArrangement.put('proposeDate', objPropose.Course_Date__c);
                    mapProposeArrangement.put('proposeLocation', objPropose.Training_Location__r.Final_Address__c);
                    mapProposeArrangement.put('description', objPropose.Description__c);
                }
                mapFeild2Value.put('lstArrangemtInfo', lstArrangemtInfo);
                mapFeild2Value.put('lstProposeArrangement', lstProposeArrangement);
            }
            system.debug('mapFeild2Value--->'+mapFeild2Value);
            return mapFeild2Value;

        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage() + '报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static string approvalOrRejectRegister(String JsonApprovalInfoString){
        try {
            return CCM_CourseRegisterPreviewCtl.approvalOrRejectRegister( JsonApprovalInfoString);

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static string upsertProposeArrangement(String jsonData){
        try {
            Map<String,Object> mapFeild2Value = (Map<String,Object>)JSON.deserializeUntyped(jsonData);
            Propose_Course_Arrangement__c objPropose = new Propose_Course_Arrangement__c();
            String proposeId = (String)mapFeild2Value.get('proposeId');//修改时传入
            String proposeLocation = (String)mapFeild2Value.get('proposeLocation');
            String description = (String)mapFeild2Value.get('description');
            if((String)mapFeild2Value.get('proposeStartTime') != null){
                String stringValue = (String)mapFeild2Value.get('proposeStartTime') ;
                List<String> timeComponents = stringValue.split(':');
                Integer hours = Integer.valueOf(timeComponents[0]);
                Integer minutes = Integer.valueOf(timeComponents[1]);
                Integer seconds = Integer.valueOf(timeComponents[2]);
                Time timeValue = Time.newInstance(hours, minutes, seconds, 0);
                objPropose.Start_Time__c = timeValue;

            }
            if((String)mapFeild2Value.get('proposeEndTime') != null){
                String stringValue = (String)mapFeild2Value.get('proposeEndTime') ;
                List<String> timeComponents = stringValue.split(':');
                Integer hours = Integer.valueOf(timeComponents[0]);
                Integer minutes = Integer.valueOf(timeComponents[1]);
                Integer seconds = Integer.valueOf(timeComponents[2]);
                Time timeValue = Time.newInstance(hours, minutes, seconds, 0);
                objPropose.End_Time__c = timeValue;

            }

            // Time proposeStartTime = mapFeild2Value.get('proposeStartTime') == null ? null :  Time.parse((String)mapFeild2Value.get('proposeStartTime'));
            // Time proposeEndTime = mapFeild2Value.get('proposeEndTime') == null  ? null : Time.parse((String)mapFeild2Value.get('proposeEndTime'));
            Date proposeDate = mapFeild2Value.get('proposeDate') == null  ? null : Date.valueOf((String)mapFeild2Value.get('proposeDate')) ;
            String courseSetting = (String)mapFeild2Value.get('courseSetting');

            if(String.isNotBlank(proposeId)){
                objPropose.Id = proposeId;
            }


            objPropose.Course_Date__c = proposeDate;
            if(String.isNotBlank(proposeLocation)){
                objPropose.Training_Location__c = proposeLocation;

            }

            objPropose.Description__c = description;
            objPropose.Training_Course_Setting__c = courseSetting;
            upsert objPropose;
            return objPropose.Id;
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static List<Map<String,Object>> queryLocationLookUpInfo(){
        try {
            List<Map<String,Object>> lstmapLocationInfo = new List<Map<String,Object>>();
            List<Training_Location__c> lstLocationInfo = [
                SELECT Id,Name,Final_Address__c FROM Training_Location__c LIMIT 5000
            ];
            for(Training_Location__c objTrainingLocation : lstLocationInfo){
                Map<String,Object> mapLocationInfo = new Map<String,Object>();
                mapLocationInfo.put('name', objTrainingLocation.Final_Address__c);
                mapLocationInfo.put('id', objTrainingLocation.Id);
                lstmapLocationInfo.add(mapLocationInfo);
            }
            return lstmapLocationInfo;

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static String  RegisterSubmit(String JsonApprovalInfoString){
        return CCM_CourseRegisterPreviewCtl.RegisterSubmit(JsonApprovalInfoString);

    }
    @AuraEnabled
    public static Map<String,Object> queryRegisterInfo(String registerId){
        return CCM_CourseRegisterPreviewCtl.queryRegisterInfo(registerId);
    }
    @AuraEnabled
    public static Map<String,Object> queryCourseArrangementInfo(String courseArrangementId,String customerId){
        system.debug('前端参数--->'+courseArrangementId);
        try {
            //根据Id查询Arrangement详细信息
            Map<String,Object> mapFeild2Value = new Map<String,Object>();
            List<Course_Arrangement__c> lstCourse = new  List<Course_Arrangement__c>();
            lstCourse = [
                SELECT Id,Training_Course_Setting__c,Training_Course_Setting__r.Course_Name__c,Training_Course_Setting__r.Course_Name__r.Name,
                Training_Course_Setting__r.Course_Desc__c,Course_Date__c,End_Time__c,Start_Time__c,Training_Location__c,Price__c,CurrencyIsoCode,
                Training_Location__r.Name
                FROM Course_Arrangement__c WHERE Id = :courseArrangementId AND Status__c = 'Launched'
            ];
            if(lstCourse ==null || lstCourse.size() == 0){

                return mapFeild2Value;
            }
            Course_Arrangement__c objCourse = lstCourse[0];

            //获取当前用户名字
            Map<String,String> mapCustomerInfo = new Map<String,String>();
            String customerName = '';
            if(String.isNotBlank(customerId)){
                //customerId不为空表示是CRM端。通过Id查询Customer Info
                Account objAccount = [
                    SELECT Id,Name FROM Account where Id = :customerId LIMIT 1
                ];
                customerName = objAccount.Name;
            }else{
                mapCustomerInfo =  CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
                if(mapCustomerInfo != null ){
                    customerId =  mapCustomerInfo.get('customerId');
                    customerName =  mapCustomerInfo.get('CustomerName');
                }

            }
            mapFeild2Value.put('customerId', customerId);
            mapFeild2Value.put('customerName', customerName);
            mapFeild2Value.put('courseArrangementId', objCourse.Id);
            mapFeild2Value.put('trainingCourseName', objCourse.Training_Course_Setting__r.Course_Name__r.Name);
            mapFeild2Value.put('trainingCourseId', objCourse.Training_Course_Setting__r.Course_Name__c);
            String strDesc = objCourse.Training_Course_Setting__r.Course_Desc__c;
                if (String.isNotBlank(strDesc) && strDesc.contains(strRichTextImageUrl)) {
                    strDesc = strDesc.replace(strRichTextImageUrl, Label.CCM_File_Storage_Url + strRichTextImageUrl);
                    strDesc = strDesc.replace('amp;','');
                    strDesc = strDesc.replace('/;','');
                }
            mapFeild2Value.put('trainingDescription', strDesc);
            mapFeild2Value.put('trainingStartTime',objCourse.Start_Time__c ==  null ? null : DateTime.newInstance(Date.today(), objCourse.Start_Time__c).format('HH:mm'));
            mapFeild2Value.put('trainingEndTime',objCourse.End_Time__c ==  null ? null :  DateTime.newInstance(Date.today(), objCourse.End_Time__c).format('HH:mm'));
            mapFeild2Value.put('trainingDate', objCourse.Course_Date__c);
            mapFeild2Value.put('trainingLocation', objCourse.Training_Location__r.Name);
            mapFeild2Value.put('price', objCourse.Price__c == null ? 0 : objCourse.Price__c);
            mapFeild2Value.put('currency', objCourse.CurrencyIsoCode);
            //根据customer查询Payment Term 信息
            List<Sales_Program__c> lstSalesProgram = new List<Sales_Program__c>();
            lstSalesProgram = [
                SELECT Id,Payment_Term__c,Customer__c FROM Sales_Program__c WHERE Customer__c = :customerId
                AND Status__c = 'Active' AND Order_Type__c = :Label.SalesPrice_OrderType
            ];
            if(lstSalesProgram != null && lstSalesProgram.size()>0){
                mapFeild2Value.put('paymentTerm',lstSalesProgram[0].Payment_Term__c);
            }
            //根据Customer查询Address地址信息
            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            lstAddress =    [
                SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                RecordType.Id, RecordType.DeveloperName,Company_name_1__c
                FROM Account_Address__c WHERE  Customer__c = :customerId  AND
                RecordType.DeveloperName IN  ('Billing_Address','Shipping_Address') AND StatusNew__c ='Active' AND Final_Address__c <> NULL
            ];
            //默认传一个Address
            Map<String,Object> mapBillAddressInfo = new Map<String,Object>();
            Map<String,Object> mapShipAddressInfo = new Map<String,Object>();
            if(lstAddress != null && lstAddress.size()>0 ){
                for(Account_Address__c objAddress : lstAddress){
                    if(objAddress.RecordType.DeveloperName == 'Billing_Address'){
                        mapBillAddressInfo.put('id', objAddress.Id);
                        mapBillAddressInfo.put('name', objAddress.Final_Address__c);
                        mapBillAddressInfo.put('country', objAddress.Country__c);
                        mapBillAddressInfo.put('city', objAddress.City__c);
                        mapBillAddressInfo.put('address', objAddress.Street_1__c);
                        mapBillAddressInfo.put('postalCode', objAddress.Postal_Code__c);
                        mapBillAddressInfo.put('companyName', objAddress.Company_name_1__c);

                    }else if(objAddress.RecordType.DeveloperName == 'Shipping_Address'){
                        mapShipAddressInfo.put('id', objAddress.Id);
                        mapShipAddressInfo.put('name', objAddress.Final_Address__c);
                        mapShipAddressInfo.put('country', objAddress.Country__c);
                        mapShipAddressInfo.put('city', objAddress.City__c);
                        mapBillAddressInfo.put('address', objAddress.Street_1__c);
                        mapShipAddressInfo.put('postalCode', objAddress.Postal_Code__c);
                        mapShipAddressInfo.put('companyName', objAddress.Company_name_1__c);

                    }


                }

            }
            mapFeild2Value.put('BillAddressInfo', mapBillAddressInfo);
            mapFeild2Value.put('ShipAddressInfo', mapShipAddressInfo);
            return mapFeild2Value;

        } catch (Exception e) {
            system.debug('报错行数---->'+e.getLineNumber()+'报错信息---->'+e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    /**
     * Author : Honey
     * Date 2023/07/31
     * Description: 临时保存文件。供前端预览文件
     */
    @AuraEnabled
    public static string uploadFileMidel( String content,String uploadFileName,String fileName){
        Map<String, String> result = new Map<String, String>();

        try {
            ContentVersion conVer = new ContentVersion();
            conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
            conVer.PathOnClient = uploadFileName; // The files name, extension is very important here which will help the file in preview.
            conVer.Title = FileName + String.valueOf(Datetime.now()); // Display name of the files
            conVer.VersionData = EncodingUtil.base64Decode(content); // converting your binary string to Blog
            insert conVer;
            ContentVersion objContentVersion = [
                SELECT Id,ContentDocumentId FROM ContentVersion WHERE Id = :conVer.Id
            ];
            system.debug('objContentVersion--->'+objContentVersion.ContentDocumentId);
            result.put('Status', 'Success');
            result.put('Message', '');
            result.put('ContentId', objContentVersion.ContentDocumentId);
            return JSON.serialize(result);

        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            result.put('Status', 'Error');
            result.put('Message', e.getMessage());
            result.put('ContentId', '');
            return JSON.serialize(result);
        }
    }

    @AuraEnabled
    public static void uploadFile(String recordId,List<CCM_PurchaseOrderPreview.uploadFileInfo> lstuploadFileInfo){
        system.debug('lstuploadFileInfo--->'+lstuploadFileInfo);
        List<Purchase_Order_Attachment__c> lstdeleteAttachment = [
            SELECT Id,Course_Register__c,File_Id__c FROM Purchase_Order_Attachment__c WHERE Course_Register__c = :recordId
        ];
        Set<String> lstContentDocumentId = new Set<String>();
        Set<String> setInsertDocument = new Set<String>();


        delete lstdeleteAttachment;
        List<Purchase_Order_Attachment__c> lstinsertPurchaseOrder = new List<Purchase_Order_Attachment__c>();
        for(CCM_PurchaseOrderPreview.uploadFileInfo objFileInfo : lstuploadFileInfo){
            Purchase_Order_Attachment__c objPurchaseAttachment = new Purchase_Order_Attachment__c();
            objPurchaseAttachment.Course_Register__c = recordId;
            objPurchaseAttachment.File_Date__c = objFileInfo.fileDate;
            setInsertDocument.add(objFileInfo.contentId);
            objPurchaseAttachment.File_Id__c = objFileInfo.contentId;
            objPurchaseAttachment.File_Name__c = objFileInfo.fileName;
            objPurchaseAttachment.File_Type__c = objFileInfo.fileType;
            lstinsertPurchaseOrder.add(objPurchaseAttachment);

        }
        for(Purchase_Order_Attachment__c objAttachment : lstdeleteAttachment){
            if(!setInsertDocument.contains(objAttachment.File_Id__c)){
                lstContentDocumentId.add(objAttachment.File_Id__c);
            }
        }
        //根据AttachmentId删除文件
        List<ContentDocument> lstContent = [SELECT  Id FROM ContentDocument  WHERE Id IN :lstContentDocumentId];
        delete lstContent;

        insert lstinsertPurchaseOrder;

    }
    //与CRM共用
    @AuraEnabled
    public static string upsertRegisterInfo(String jsonData,String lstparticipants){
        system.debug('前端请求参数---->'+jsonData);
        system.debug('lstparticipants-->'+lstparticipants);

        try {
            Map<String,String> mapFeild2ValueRequest =  (Map<String,String>)JSON.deserializeStrict(jsonData, Map<String,String>.class);
            List<Map<String,String>> lstmapParticipantsInfo =  (List<Map<String,String>>)JSON.deserializeStrict(lstparticipants,List<Map<String,String>>.class);

            String courseArrangementId = mapFeild2ValueRequest.get('courseArrangementId');
            String courseRegisterId = mapFeild2ValueRequest.get('courseRegisterId'); //更新的时候传
            //添加座位校验
            system.debug('courseArrangementId-->'+courseArrangementId);
            Course_Register__c objCourseRegister = new Course_Register__c();
            String paymentTerm = mapFeild2ValueRequest.get('paymentTerm');
            String billAddressId = mapFeild2ValueRequest.get('billAddressId');
            String shipAddressId = mapFeild2ValueRequest.get('shipAddressId');
            system.debug('shipAddressId======>'+mapFeild2ValueRequest.get('shipAddressId'));
            String customerId = mapFeild2ValueRequest.get('customerId');
            objCourseRegister.Shiping_Address__c = shipAddressId;
            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            /*lstAddress =    [
                SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                RecordType.Id, RecordType.DeveloperName,Company_name_1__c,Primary__c
                FROM Account_Address__c WHERE  Customer__c = :customerId  AND
                RecordType.DeveloperName = 'Shipping_Address' AND StatusNew__c ='Active' AND Final_Address__c <> NULL
            ];
            if(lstAddress != null  && lstAddress.size()>0){
                for(Account_Address__c objAccount : lstAddress){
                    if(objAccount.Primary__c){
                        objCourseRegister.Shiping_Address__c = objAccount.Id;
                        break;
                    }

                }
                //如果没有找到primary的话随便抓一个
                if(String.isBlank(objCourseRegister.Shiping_Address__c) ){
                    objCourseRegister.Shiping_Address__c = lstAddress[0].Id;


                }

            }
            */

            String jsonInfo = mapFeild2ValueRequest.get('jsonInfo');
            objCourseRegister.Billing_Address__c = billAddressId;
            List<String> lstparticipantsInfo = new List<String>();
            for(Map<String,String> mapParticpantsInfo : lstmapParticipantsInfo){
                system.debug('111--->'+mapParticpantsInfo.get('participants'));
                lstparticipantsInfo.add( mapParticpantsInfo.get('participants'));
            }
            system.debug('lstparticipantsInfo-->'+lstparticipantsInfo);
            objCourseRegister.Trainee__c = lstparticipantsInfo.toString();
            objCourseRegister.Customer__c = customerId;
            objCourseRegister.Payment_Term__c = paymentTerm;
            objCourseRegister.JsonInfo__c = jsonInfo;
            String pcs = mapFeild2ValueRequest.get('pcs');
            Decimal Slot = Decimal.valueOf(pcs);
            Course_Arrangement__c objCourseArrange = [
                SELECT Id,Avaliable_Slot__c,Price__c,Traning_PriceBook__c,Traning_PriceBook__r.CurrencyIsoCode
                FROM Course_Arrangement__c WHERE Id = :courseArrangementId
            ];
            objCourseRegister.Course_Arrangement__c = courseArrangementId;


            if(objCourseArrange.Avaliable_Slot__c < Slot){
                return Label.Service_Register_no_Slot;
            }
            objCourseRegister.PCS__c = Slot;
            String trainingCourseId = mapFeild2ValueRequest.get('trainingCourseId');
            objCourseRegister.Training_Course__c = trainingCourseId;
            objCourseRegister.Price_Pcs__c = objCourseArrange.Price__c;
            objCourseRegister.CurrencyIsoCode = objCourseArrange.Traning_PriceBook__r.CurrencyIsoCode;
            if(String.isNotBlank(courseRegisterId)){
                //没有Arrangement表示是CRM端修改。触发发送邮件
                //sendEditEmailToOwner(courseRegisterId);
                objCourseRegister.Id = courseRegisterId;
            }else{
                objCourseRegister.Status__c = 'Draft';

            }
            upsert objCourseRegister;
            //根据PCS创建Item数据
            List<Course_Register_Item__c> lstCourse = new List<Course_Register_Item__c>();
            if(objCourseRegister.PCS__c != null){
                //查询item
                List<Course_Register_Item__c> lstDeleteItem = new List<Course_Register_Item__c>();
                lstDeleteItem = [
                    SELECT Id FROM Course_Register_Item__c WHERE Course_Register__c = : objCourseRegister.Id
                ];
                DELETE lstDeleteItem;
                for(Integer i =0 ; i< objCourseRegister.PCS__c ; i++){
                    Course_Register_Item__c objCourse = new Course_Register_Item__c();
                    objCourse.Course_Arrangement__c = courseArrangementId;
                    objCourse.Order_Date__c = Date.today();
                    objCourse.Course_Register__c = objCourseRegister.Id;
                    Map<String,String> mapParticpantsInfo = lstmapParticipantsInfo[i];
                    objCourse.Trainee__c = mapParticpantsInfo.get('participants');
                    objCourse.tele_SMS__c = mapParticpantsInfo.get('SMS');
                    objCourse.email__c = mapParticpantsInfo.get('email');
                    objCourse.Remark__c = mapParticpantsInfo.get('remark');
                    lstCourse.add(objCourse);
                }
                insert lstCourse;

            }

            return objCourseRegister.Id;
        } catch (Exception e) {
            system.debug('报错信息---->'+e.getMessage()+'报错行数----->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    //CRM端选择CourseSetting后保存数据
    @AuraEnabled
    public static void upsertRegisterCourseSetting(String JsonData){
        system.debug('前端请求参数---->'+jsonData);
        try {
            Map<String,String> mapFeild2ValueRequest =  (Map<String,String>)JSON.deserializeStrict(jsonData, Map<String,String>.class);
            String courseRegisterId = mapFeild2ValueRequest.get('courseRegisterId'); //这个时候肯定有Id
            String trainingCourseId = mapFeild2ValueRequest.get('trainingCourseId');
            //通过registerId查询信息
            Course_Register__c objRegister = [
                SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,
                CreatedById,CreatedBy.Name  FROM Course_Register__c  WHERE Id = :courseRegisterId
            ];
            objRegister.Training_Course__c = trainingCourseId;
            objRegister.Status__c = 'Draft';

            update objRegister;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static Map<String,Object> queryOrderInfo(String recordId){
        try {
            return CCM_TrainingOrderInfo.queryOrderInfo( recordId);

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    //CRM端选择CourseArrangement后保存数据
    @AuraEnabled
    public static String upsertRegisterCourseArrangement(String JsonData){
        system.debug('前端请求参数---->'+jsonData);
        try {
            Map<String,String> mapFeild2ValueRequest =  (Map<String,String>)JSON.deserializeStrict(jsonData, Map<String,String>.class);
            String courseArrangementId = mapFeild2ValueRequest.get('courseArrangementId');
            String courseRegisterId = mapFeild2ValueRequest.get('courseRegisterId'); //这个时候肯定有Id
            //通过registerId查询信息
            Course_Register__c objRegister = [
                SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,
                CreatedById,CreatedBy.Name  FROM Course_Register__c  WHERE Id = :courseRegisterId
            ];
            //添加座位校验
            system.debug('courseArrangementId-->'+courseArrangementId);
            Course_Register__c objCourseRegister = new Course_Register__c();
            if(String.isNotBlank(courseArrangementId)){
                Course_Arrangement__c objCourseArrange = [
                    SELECT Id,Avaliable_Slot__c,Price__c,Traning_PriceBook__c,Traning_PriceBook__r.CurrencyIsoCode
                    FROM Course_Arrangement__c WHERE Id = :courseArrangementId
                ];

                String pcs = mapFeild2ValueRequest.get('pcs');
                Decimal Slot = Decimal.valueOf(pcs);

                if(objCourseArrange.Avaliable_Slot__c < Slot){
                    return Label.Service_Register_no_Slot;
                }
                objCourseRegister.PCS__c = Slot;
                objCourseRegister.Course_Arrangement__c = courseArrangementId;
                String trainingCourseId = mapFeild2ValueRequest.get('trainingCourseId');

                objCourseRegister.Price_Pcs__c = objCourseArrange.Price__c;
                objCourseRegister.CurrencyIsoCode = objCourseArrange.Traning_PriceBook__r.CurrencyIsoCode;

            }

            update objRegister;

            return null;
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static List<Map<String,String>> queryBillAddressInfo(String addressName,String customerId){
        system.debug('AddressName--->'+AddressName);
        try {

            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            if(String.isNotBlank(AddressName)){
                AddressName = '%'+AddressName+'%';
                lstAddress =    [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE Final_Address__c LIKE :AddressName AND Customer__c = :customerId  AND
                    RecordType.DeveloperName = 'Billing_Address' AND StatusNew__c = 'Active' AND Final_Address__c != NULL
                ];
            }else{
                lstAddress = [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE Customer__c = :customerId  AND
                    RecordType.DeveloperName =  'Billing_Address' AND StatusNew__c = 'Active' AND Final_Address__c !=  NULL
                ];
            }
            List<Map<String,String>> lstAddressInfo = new List<Map<String,String>>();
            system.debug('lstAddress--->'+lstAddress);
            for(Account_Address__c objAccount : lstAddress){
                Map<String,String> mapAddressInfo = new Map<String,String>();
                mapAddressInfo.put('id', objAccount.Id);
                mapAddressInfo.put('name', objAccount.Final_Address__c);
                mapAddressInfo.put('country', objAccount.Country__c);
                mapAddressInfo.put('city', objAccount.City__c);
                mapAddressInfo.put('address', objAccount.Street_1__c);
                mapAddressInfo.put('postalCode',objAccount.Postal_Code__c);
                mapAddressInfo.put('companyName', objAccount.Company_name_1__c);
                mapAddressInfo.put('primary', objAccount.Primary__c == true ? 'true' : 'false');
                lstAddressInfo.add(mapAddressInfo);
            }
            system.debug('lstAddressInfo--->'+lstAddressInfo);
            return lstAddressInfo;


        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static List<Map<String,String>> queryShipAddressInfo(String addressName,String customerId){
        system.debug('AddressName--->'+AddressName);
        try {

            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            if(String.isNotBlank(AddressName)){
                AddressName = '%'+AddressName+'%';
                lstAddress =    [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE Final_Address__c LIKE :AddressName AND Customer__c = :customerId  AND
                    RecordType.DeveloperName = 'Shipping_Address' AND StatusNew__c = 'Active' AND Final_Address__c <> NULL
                ];
            }else{
                lstAddress = [
                    SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                    RecordType.Id, RecordType.Name,Company_name_1__c,Primary__c
                    FROM Account_Address__c WHERE Customer__c = :customerId  AND
                    RecordType.DeveloperName =  'Shipping_Address' AND StatusNew__c = 'Active' AND Final_Address__c <> NULL
                ];
            }
            List<Map<String,String>> lstAddressInfo = new List<Map<String,String>>();
            for(Account_Address__c objAccount : lstAddress){
                Map<String,String> mapAddressInfo = new Map<String,String>();
                mapAddressInfo.put('id', objAccount.Id);
                mapAddressInfo.put('name', objAccount.Final_Address__c);
                mapAddressInfo.put('country', objAccount.Country__c);
                mapAddressInfo.put('city', objAccount.City__c);
                mapAddressInfo.put('address', objAccount.Street_1__c);
                mapAddressInfo.put('postalCode',objAccount.Postal_Code__c);
                mapAddressInfo.put('companyName', objAccount.Company_name_1__c);
                mapAddressInfo.put('primary', objAccount.Primary__c == true ? 'true' : 'false');
                lstAddressInfo.add(mapAddressInfo);
            }
            return lstAddressInfo;


        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    //CRM端查询可选customer
    @AuraEnabled
    public static List<Map<String,String>> queryAvaliableCustomer(String queryName){
        try {
            List<Map<String,String>> lstUserInfo = new List<Map<String,String>>();
            List<Account> lstAccount = new List<Account>();
            //查询所有Protal端的customer
            if(String.isNotBlank(queryName)){
                queryName = '%'+queryName+'%';
                lstAccount = [
                    SELECT Id,Name,Account_Description__c,IsPartner,Company__c,OwnerId,Owner.FirstName,Owner.LastName,AccountNumber,Distributor_or_Dealer__c,CurrencyIsoCode,
                    Tier__c,Sales_Channel__c,Classification1__c,Classification2__c,Association_Type__c,Customer_Business_Type__c,
                    Association_Group__r.Association_Type__c,Association_Membership_No__c,Association_Group__r.Name,
                    Intended_Brand__c FROM Account WHERE  RecordType.developerName in ('Channel','Association_Group') AND Name LIKE : queryName And Status__c in ('Active','DisContinue')
                ];
            }else{
                lstAccount = [
                    SELECT Id,Name,IsPartner,Account_Description__c,Company__c,OwnerId,Owner.Name,Owner.FirstName,Owner.LastName,AccountNumber,Distributor_or_Dealer__c,CurrencyIsoCode,
                    Tier__c,Sales_Channel__c,Classification1__c,Classification2__c,Association_Type__c,Customer_Business_Type__c,
                    Association_Group__r.Association_Type__c,Association_Membership_No__c,Association_Group__r.Name,
                    Intended_Brand__c FROM Account WHERE RecordType.developerName in ('Channel','Association_Group') And Status__c in ('Active','DisContinue')
                ];

            }
            if(lstAccount != null && lstAccount.size()>0){
                for(Account objAccount : lstAccount){
                    Map<String,String> mapAccountInfo = new Map<String,String>();
                    mapAccountInfo.put('customerId', objAccount.Id == null ? ' ' : objAccount.Id );
                    mapAccountInfo.put('ownerName', (objAccount.Owner.FirstName == null ? '' :objAccount.Owner.FirstName )+' '+(objAccount.Owner.LastName == null ? '' : objAccount.Owner.LastName));
                    mapAccountInfo.put('company', objAccount.Company__c == null ? ' ' : objAccount.Company__c );
                    mapAccountInfo.put('name', objAccount.Name == null ? ' ' : objAccount.Name );
                    mapAccountInfo.put('accountNumber', objAccount.AccountNumber == null ? ' ' : objAccount.AccountNumber );
                    mapAccountInfo.put('currencyCode', objAccount.CurrencyIsoCode == null ? ' ' : objAccount.CurrencyIsoCode );
                    mapAccountInfo.put('customerType', objAccount.Distributor_or_Dealer__c == null ? ' ' : objAccount.Distributor_or_Dealer__c );
                    mapAccountInfo.put('tier', objAccount.Tier__c == null ? ' ' : objAccount.Tier__c );
                    mapAccountInfo.put('customerBusinessType', objAccount.Customer_Business_Type__c == null ? ' ' : objAccount.Customer_Business_Type__c );
                    mapAccountInfo.put('salesChannel', objAccount.Sales_Channel__c == null ? ' ' : objAccount.Sales_Channel__c );
                    mapAccountInfo.put('classification1', objAccount.Classification1__c == null ? ' ' : objAccount.Classification1__c );
                    mapAccountInfo.put('intendedBrand', objAccount.Intended_Brand__c == null ? ' ' : objAccount.Intended_Brand__c );
                    mapAccountInfo.put('associationGroup', objAccount.Association_Group__r.Association_Type__c == null ? ' ' : objAccount.Association_Group__r.Association_Type__c );
                    mapAccountInfo.put('associationmenberShip', objAccount.Association_Membership_No__c == null ? ' ' : objAccount.Association_Membership_No__c );
                    mapAccountInfo.put('associationName', objAccount.Association_Group__r.Name == null ? ' ' : objAccount.Association_Group__r.Name  );
                    mapAccountInfo.put('accountDescription', objAccount.Account_Description__c == null ? ' ' : objAccount.Account_Description__c  );
                    lstUserInfo.add(mapAccountInfo);
                }
            }
            return lstUserInfo;


        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    //CRM端通过Id带出AB的payment term以及Address信息
    @AuraEnabled
    public static Map<String,Object> queryAddressByCustomer(String customerId){
        Map<String,Object> mapFeild2Value = new Map<String,Object>();
        try {
            //
            //根据customer查询Payment Term 信息
            List<Sales_Program__c> lstSalesProgram = new List<Sales_Program__c>();
            lstSalesProgram = [
                SELECT Id,Payment_Term__c,Customer__c FROM Sales_Program__c WHERE Customer__c = :customerId
                AND Status__c = 'Active' AND Order_Type__c = :Label.SalesPrice_OrderType
            ];
            if(lstSalesProgram != null && lstSalesProgram.size()>0){
                mapFeild2Value.put('paymentTerm',lstSalesProgram[0].Payment_Term__c);
                mapFeild2Value.put('authBrandId',lstSalesProgram[0].Id);
            }
            //根据Customer查询Address地址信息
            List<Account_Address__c> lstAddress = new List<Account_Address__c>();
            lstAddress =    [
                SELECT Name,Active__c,Final_Address__c,Country__c,Customer__c,City__c,Street_1__c,Postal_Code__c,
                RecordType.Id, RecordType.DeveloperName,Company_name_1__c
                FROM Account_Address__c WHERE  Customer__c = :customerId  AND
                (RecordType.DeveloperName =  'Billing_Address' OR RecordType.DeveloperName = 'Shipping_Address') AND StatusNew__c ='Active'
                AND Final_Address__c != null
            ];
            //默认传一个Address
            List<Map<String,Object>> lstmapBillAddressInfo = new List<Map<String,Object>>();
            List<Map<String,Object>> lstmapShipAddressInfo = new List<Map<String,Object>>();
            if(lstAddress != null && lstAddress.size()>0 ){
                for(Account_Address__c objAddress : lstAddress){
                    if(objAddress.RecordType.DeveloperName ==  'Billing_Address'){
                        Map<String,Object> mapBillAddressInfo = new Map<String,Object>();
                        mapBillAddressInfo.put('id', objAddress.Id);
                        mapBillAddressInfo.put('name', objAddress.Final_Address__c);
                        mapBillAddressInfo.put('country', objAddress.Country__c);
                        mapBillAddressInfo.put('city', objAddress.City__c);
                        mapBillAddressInfo.put('address', objAddress.Street_1__c);
                        mapBillAddressInfo.put('postalCode', objAddress.Postal_Code__c);
                        mapBillAddressInfo.put('companyName', objAddress.Company_name_1__c);
                        lstmapBillAddressInfo.add(mapBillAddressInfo);
                    }else if(objAddress.RecordType.DeveloperName ==  'Shipping_Address'){
                        Map<String,Object> mapShipAddressInfo = new Map<String,Object>();
                        mapShipAddressInfo.put('id', objAddress.Id);
                        mapShipAddressInfo.put('name', objAddress.Final_Address__c);
                        mapShipAddressInfo.put('country', objAddress.Country__c);
                        mapShipAddressInfo.put('city', objAddress.City__c);
                        mapShipAddressInfo.put('address', objAddress.Street_1__c);
                        mapShipAddressInfo.put('postalCode', objAddress.Postal_Code__c);
                        mapShipAddressInfo.put('companyName', objAddress.Company_name_1__c);
                        lstmapShipAddressInfo.add(mapShipAddressInfo);

                    }
                }



            }
            if(lstmapBillAddressInfo != null && lstmapBillAddressInfo.size() == 1){
                mapFeild2Value.put('billaddressInfo', lstmapBillAddressInfo[0]);
            }
            if(lstmapShipAddressInfo != null && lstmapShipAddressInfo.size() == 1){
                mapFeild2Value.put('shipaddressInfo', lstmapShipAddressInfo[0]);
            }
            system.debug('mapFeild2Value-->'+mapFeild2Value);


            return mapFeild2Value;

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Map<String,String> GetCurrentUserCustomer(){
        try {
            return CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    //分页
    public static List<Map<String,Object>> getCurrentData(List<Map<String,Object>> allData, Decimal pageNumber, Integer pageSize){
        List<Map<String,Object>> currentData = new List<Map<String,Object>>();
        Integer min = ((Integer) pageNumber - 1) * pageSize;
        Integer max = (Integer) pageNumber * pageSize - 1;
        for (Integer i = min; i <= max; i++){
            if (i < allData.size()){
                currentData.add(allData.get(i));
            }
        }
        return currentData;
    }
    //Inside Sales修改后发邮件到
    @AuraEnabled
    public static void sendEditEmailToOwner(String recordId){
        try {
            //审核通过发送邮件到创建人
            List<EmailTemplate> lstEmailTemplate = new List<EmailTemplate>();
            lstEmailTemplate = [
                Select e.Id, e.Name from EmailTemplate e where Name  = 'Training Update Email'
            ];
            if(lstEmailTemplate != null && lstEmailTemplate.size()>0){
                String templateId = lstEmailTemplate[0].Id;
                //通过recordId查询Register的创建人
                List<Course_Register__c> lstCourse = new List<Course_Register__c>();
                lstCourse = [
                    SELECT Id,CreatedById,CreatedBy.Email,CreatedBy.IsActive,Status__c,Customer__c  FROM Course_Register__c
                    WHERE CreatedBy.IsActive = TRUE AND Id = :recordId
                ];
                List<String> lstCustomerString = new List<String>();

                for(Course_Register__c objRegister : lstCourse){
                    lstCustomerString.add(objRegister.Customer__c);
                }
                //通过CustomerId查找相关的用户
                List<User> lstUser = new List<User>();
                lstUser = [
                    SELECT
                    ContactId, Contact.Account_Name__c, Contact.AccountId ,CurrencyIsoCode,Email
                    FROM User   WHERE  Contact.AccountId in : lstCustomerString
                ];

                if(lstCourse != null && lstCourse.size()>0){

                    List<String> lstReceiptEmail = new List<String>();
                    for(Course_Register__c objCourse : lstCourse){

                        if(objCourse.Status__c != 'Draft' && objCourse.CreatedById != UserInfo.getUserId()){
                            //非草稿状态并且修改人不是创建人需要发邮件到创建人
                            lstReceiptEmail.add(objCourse.CreatedBy.Email);
                            if(lstUser != null && lstUser.size() > 0 ){
                                for(User objUser : lstUser){
                                    lstReceiptEmail.add(objUser.Email);
                                }
                            }
                        }else {
                            //否者不需要发送邮件
                            return;
                        }
                    }
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setTemplateId(templateId);
                    email.setTargetObjectId(UserInfo.getUserId());
                    email.setToAddresses(lstReceiptEmail);
                    //设置发件人
                    List<OrgWideEmailAddress> listAddresses = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'EGO CRM'];
                    if(listAddresses != null  && listAddresses.size() > 0){
                        email.setOrgWideEmailAddressId(listAddresses[0].Id);
                    }
                    email.setSaveAsActivity(false);
                    Messaging.SendEmailResult[] results = Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{email});
                }
            }

        } catch (Exception e) {
            system.debug('报错信息-->'+e.getMessage()+'报错行数--->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    //protal用户接收课程
    @AuraEnabled
    public static void ReceiptCourse(String courseRegisterId){
        try {
            Course_Register__c objRegister = [
                SELECT Id,Customer__c,Customer__r.Name,Customer__r.AccountNumber,Name,Order_Type__c,Status__c,Total_Amount__c,
                CreatedById,CreatedBy.Name, Course_Arrangement__c, Course_Arrangement__r.Free_Training__c
                FROM Course_Register__c  WHERE Id = :courseRegisterId
            ];
            objRegister.Status__c = 'Confirmed';

            //todo 调用接口
            update objRegister;

            // Only sync to ERP if it's not a free training
            if(objRegister.Course_Arrangement__r.Free_Training__c != true){
                pushOrderToEBS(objRegister.Id);
            }
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static void  CancelCourse(String courseRegisterId,String reason){
        CCM_CourseRegisterPreviewCtl.CancelCourse(courseRegisterId,reason);

    }

    @future (callout=true)
    public static void pushOrderToEBS(String recordId){
        CCM_TraningOrderCallout.pushRequestInfo(recordId);
    }

    // 判断 Customer 的 Country 是否为 DE
    @AuraEnabled
    public static Boolean judgeCustomerCountry(){
        //Honey Update customer改为后端直接查询
        String customerId = '';
        customerId = CCM_PortalPageUtil.getCustomerByUser(UserInfo.getUserId());
        //Honey Update End
        Account accountResult = [
            Select a.Id, a.Country__c from Account a where a.Id = :customerId
        ];

        if(accountResult != null && accountResult.Country__c == 'DE'){
            return true;
        }

        return false;
    }

    public CCM_CourseRegisterCtl() {

    }
    }