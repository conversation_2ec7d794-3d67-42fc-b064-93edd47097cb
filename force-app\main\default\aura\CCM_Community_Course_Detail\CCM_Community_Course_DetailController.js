({
    doInit : function(component, event, helper) {
        component.set("v.stepsList", [
            $A.get("$Label.c.CCM_SelectTrainingCourse"),
            $A.get("$Label.c.CCM_SelectCourseDetail"),
            $A.get("$Label.c.CCM_PreviewAndSubmit")
        ]);
        // 判断url参数
        var url = window.location.search
        console.log("url",url);
        // component.set('v.trainingid',trainingid)
        if(url.indexOf('recordId') != -1){
            console.log("是新建");
            component.set('v.trainingid',url.slice(window.location.search.length - 18))
            helper.applyTrainingCourse(component)
        }else if(url.indexOf('recordinfo') != -1){
            console.log("是编辑");
            component.set('v.RegisterId',url.slice(window.location.search.length - 18))
            component.set('v.lockselectcustomer',true)
            
            helper.queryRegisterInfo(component) // aaa
        }
        // helper.queryCourseArrangementInfo(component)
        helper.queryRegisterList(component)
    },
    doRegister : function(component, event, helper) {
        component.set('v.isBusy',true)
        let TrainingCourseInfo = component.get('v.TrainingCourseInfo')
        console.log("当前选择的课程",JSON.stringify(TrainingCourseInfo.lstArrangemtInfo[event.target.name]));
        component.set("v.currentStep", 2);
        component.set('v.CourseArrangementInfo',TrainingCourseInfo.lstArrangemtInfo[event.target.name])
        component.set("v.ArrangementId", TrainingCourseInfo.lstArrangemtInfo[event.target.name].arrangementId);
        component.set('v.CoursePrice',TrainingCourseInfo.lstArrangemtInfo[event.target.name].arrangementprice)
        helper.queryCourseArrangementInfo(component)
    },
    showProduct : function(component, event, helper) {
        let index = event.target.name || event.target.id
        let TrainingCourseInfo = component.get('v.TrainingCourseInfo')
        TrainingCourseInfo.lstArrangemtInfo[index].showProduct = !TrainingCourseInfo.lstArrangemtInfo[index].showProduct
        component.set('v.TrainingCourseInfo',TrainingCourseInfo)
    },
    // 保存草稿并跳转
    saveAsDraft : function(component, event, helper) {
        component.set('v.isBusy',true)
        helper.upsertRegisterInfo(component,component.get("v.pcs"),true,false)
    },
    // 保存草稿不跳转
    saveAsDraft : function(component, event, helper) {
        component.set('v.isBusy',true)
        helper.upsertRegisterInfo(component,component.get("v.pcs"),false,true)
    },
    // 点击的pcs的减号
    handleSubPCS : function(component, event, helper) {
        let _pcsList = JSON.parse(JSON.stringify(component.get('v.pcsList')));
        if(component.get('v.pcs') >= 1){
            component.set('v.pcs',component.get('v.pcs') - 1)
            let pcsList = []
            for (let i = 0; i < component.get('v.pcs'); i++) {
                pcsList.push({
                    participants:_pcsList[i] ? _pcsList[i].participants : "",
                    SMS:_pcsList[i] ? _pcsList[i].SMS : "",
                    email:_pcsList[i] ? _pcsList[i].email : "",
                    remark:_pcsList[i] ? _pcsList[i].remark : "",
                    index:i + 1
                })
            }
            component.set('v.pcsList',pcsList)
        }
    },
    // 自动带出BillToAddress
    changeBillToAddress : function(component, event, helper) {
        console.log("改变了changeBillToAddress",JSON.stringify(component.get('v.BillToAddress')));
        if(component.get('v.BillToAddress').country == 'DE'){
            component.set('v.VAT',component.get("v.CoursePrice") * 0.19)
            console.log("当前VAT",component.get("v.VAT"));
        }else{
            component.set('v.VAT',0)
        }
       // component.set('v.CoursePrice',TrainingCourseInfo.lstArrangemtInfo[event.target.name].arrangementprice)
    },
    changeShipToAddress : function(component, event, helper) {
        console.log("改变了ShipToAddress",JSON.stringify(component.get('v.ShipToAddress')));
    },
    // 点击pcs的加号
    handleAddPCS : function(component, event, helper) {
        let _pcsList = JSON.parse(JSON.stringify(component.get('v.pcsList')));
        component.set('v.pcs',Number(component.get('v.pcs')) + 1)
        let pcsList = []
        for (let i = 0; i < component.get('v.pcs'); i++) {
            pcsList.push({
                participants:_pcsList[i] ? _pcsList[i].participants : "",
                SMS:_pcsList[i] ? _pcsList[i].SMS : "",
                email:_pcsList[i] ? _pcsList[i].email : "",
                remark:_pcsList[i] ? _pcsList[i].remark : "",
                index:i + 1
            })
        }
        component.set('v.pcsList',pcsList)
    },
    // 输入数字
    searchHeaderPromotionByCode : function(component, event, helper) {
        let _pcsList = JSON.parse(JSON.stringify(component.get('v.pcsList')));
        if(component.get('v.pcs') < 0){
            component.set('v.pcs',0)
        }else{
            let pcsList = []
            for (let i = 0; i < component.get('v.pcs'); i++) {
                pcsList.push({
                    participants:_pcsList[i] ? _pcsList[i].participants : "",
                    SMS:_pcsList[i] ? _pcsList[i].SMS : "",
                    email:_pcsList[i] ? _pcsList[i].email : "",
                    remark:_pcsList[i] ? _pcsList[i].remark : "",
                    index:i + 1
                })
            }
            component.set('v.pcsList',pcsList)
        }
    },
    // 点击下一步
    nextStep : function(component, event, helper) {
        if(component.get("v.currentStep") == 2){
            if(!component.get('v.pcsList').every(item =>item.participants)){
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": $A.get("$Label.c.CCM_Error"),
                    "message": $A.get("$Label.c.CCM_InputParticipantsError"),
                    "type": "error",
                }).fire();
            }else{
                component.set('v.isBusy',true)
                let pcs = component.get('v.pcs')
                helper.upsertRegisterInfo(component,pcs,false)
            }
        }else if(component.get("v.currentStep") == 3){
            component.set('v.isBusy',true)
            console.log("最后一步的提交");
                helper.RegisterSubmit(component)
        }else{
            let _currentStep = component.get("v.currentStep") + 1
            component.set("v.currentStep", _currentStep);
        }
    },
    // 点击建议课程时间安排
    ClickPropose : function(component, event, helper) {
        component.set('v.ClickProposePupup',true)
    },
    // 点击建议课程时间的弹窗
    cancelProposeEvent : function(component, event, helper) {
        component.set('v.ClickProposePupup',false)
    },
    // 点击提交建议课程时间的信息
    onclickProposeSubmit : function(component, event, helper) {
        let _SbumitProposeInfo = component.get('v.SbumitProposeInfo')
        if(!_SbumitProposeInfo.EndTime || !_SbumitProposeInfo.StartTime || !_SbumitProposeInfo.location.id || !_SbumitProposeInfo.date || !_SbumitProposeInfo.description){
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_EnterValueError"),
                "type": "error",
            }).fire();
        }else if(_SbumitProposeInfo.EndTime && _SbumitProposeInfo.StartTime && _SbumitProposeInfo.EndTime < _SbumitProposeInfo.StartTime){
            var toastEvt = $A.get("e.force:showToast");
            toastEvt.setParams({
                "title": $A.get("$Label.c.CCM_Error"),
                "message": $A.get("$Label.c.CCM_StartTimeLaterThanEndTimeError"),
                "type": "error",
            }).fire();
        }else{
            helper.upsertProposeArrangement(component)
        }
    },
    // 返回上一步
    onPrevious : function(component, event, helper) {
        let _currentStep = component.get("v.currentStep") - 1
        component.set("v.currentStep", _currentStep);
    },
    // 返回tab
    onCancel : function(component, event, helper) {
        var url = '/s/Training';
        window.open(url,"_self");
    },
})