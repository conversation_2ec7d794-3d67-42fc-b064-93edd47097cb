<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId"
    controller="CCM_CourseArrangementPublishCtl">
    <aura:attribute name="recordId" type="String" default=""/>
    <aura:attribute name="TrainingList" type="List" default='[]' />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <!-- 布局部分 -->
    <div style="position: relative;height:420px;overflow-y: scroll">
        <table>
            <tr class="list thader">
                <th>Y/N</th>
                <th style="width: 15%;">No</th>
                <th style="width: 20%;">Location</th>
                <th style="width: 20%;">EndTime</th>
                <th style="width: 20%;">StartTime</th>
                <th style="width: 20%;">Status</th>
            </tr>
            <aura:iteration items="{!v.TrainingList}" var="item">
                <tr class="list">
                    <td>
                        <lightning:input id="{!item.arrangementId}" type="checkbox" label=""  disabled="{!item.arrangementStatus == 'Launched'}" checked="{!item.onselect}"></lightning:input>
                    </td>
                    <td>{!item.arrangementNo}</td>
                    <td>{!item.arrangementLocation}</td>
                    <td>{!item.arrangementStartTime}</td>
                    <td>{!item.arrangementEndTime}</td>
                    <td>{!item.arrangementStatus}</td>
                </tr>
            </aura:iteration>
        </table>
    </div>
    <div class="footerBtn" style="margin: 15px 10px 0;text-align: right;">
        <!-- <lightning:input id="" class="isAll" type="checkbox" label="isAll"  disabled="" checked="{!v.selectall}" onchange="{!c.selectall}"></lightning:input> -->
        <lightning:button class="field-required" variant="brand" label="Launch" title="Launch" onclick="{!c.Launch}"/>
        <lightning:button class="field-required" variant="Neutral" label="Cancel" title="Launch" onclick="{!c.Cancel}"/>
    </div>
</aura:component>