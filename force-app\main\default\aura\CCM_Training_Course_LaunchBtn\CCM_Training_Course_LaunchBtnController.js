({
    doInit : function(component, event, helper) {
        var action = component.get("c.queryAvaliablePublishList");
        action.setParams({
            JsonData : JSON.stringify({
            "courseSettingId":component.get('v.recordId'),
            "operation":"launch"
            })
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           console.log("response",JSON.parse((JSON.stringify(response))));

           if (state === "SUCCESS") {
                let TrainingList = response.getReturnValue()
                TrainingList.map(item =>{
                    item.arrangementDate ? item.arrangementStartTime = item.arrangementDate + " " + item.arrangementStartTime : ""
                    item.arrangementDate ? item.arrangementEndTime = item.arrangementDate + " " + item.arrangementEndTime : ""
                })
                component.set('v.TrainingList',TrainingList)
                console.log("result",JSON.parse((JSON.stringify(component.get('v.TrainingList')))));

           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].pageErrors[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    Launch : function(component, event, helper) {
        let _TrainingList = component.get('v.TrainingList')
        let lstListArrangementIds = []
        component.get('v.TrainingList').forEach(item => {
            if(item.onselect){
                lstListArrangementIds.push(item.arrangementId)
            }
        });

        var action = component.get("c.publishOrUnPublishArrangement");
        action.setParams({
            "lstListArrangementIds" : lstListArrangementIds,
            "operation"             : "Launch",
            "isAll"                 : false,
            "courseSettingId"       : component.get('v.recordId')
            
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                 let result = response.getReturnValue()
                 console.log("result",JSON.parse((JSON.stringify(result))));
                 $A.get("e.force:closeQuickAction").fire();
                 var toastEvt = $A.get("e.force:showToast");
                 toastEvt.setParams({
                     "title": "SUCCESS",
                     "message": "SUCCESS",
                     "type": "success"
                 }).fire();
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].pageErrors[0].message,
                    "type": "error"
                }).fire();
            }
         });
         $A.enqueueAction(action)
    },
    Cancel : function(component, event, helper) {
        $A.get("e.force:closeQuickAction").fire();
    }
})