({
    doInit : function(component, event, helper) {
        console.log("recordid",component.get('v.recordId'));
        var action = component.get("c.queryAvaliablePublishList");
        action.setParams({
            JsonData : JSON.stringify({
            "courseSettingId":component.get('v.recordId'),
            "operation":"unPublish"
            })
        });
        action.setCallback(this, function (response) {
           var state = response.getState();
           if (state === "SUCCESS") {
                let TrainingList = response.getReturnValue()
                TrainingList.map(item =>{
                    item.arrangementDate ? item.arrangementStartTime = item.arrangementDate + " " + item.arrangementStartTime : ""
                    item.arrangementDate ? item.arrangementEndTime = item.arrangementDate + " " + item.arrangementEndTime : ""
                })
                component.set('v.TrainingList',TrainingList)
                console.log("result",JSON.stringify(component.get('v.TrainingList')));

           } else {
               var toastEvt = $A.get("e.force:showToast");
               toastEvt.setParams({
                   "title": "Error",
                   "message": response.getError()[0].pageErrors[0].message,
                   "type": "error"
               }).fire();
           }
        });
        $A.enqueueAction(action);
    },
    Unpublish : function(component, event, helper) {
        let _TrainingList = component.get('v.TrainingList')
        let lstListArrangementIds = []
        let isAll = _TrainingList.filter(item => item.onselect).length == _TrainingList.filter(item => item.arrangementStatus == "Launched").length
        component.get('v.TrainingList').forEach(item => {
            if(item.onselect){
                lstListArrangementIds.push(item.arrangementId)
            }
        });

        var action = component.get("c.publishOrUnPublishArrangement");
        action.setParams({
            "lstListArrangementIds" : lstListArrangementIds,
            "operation"             : "Unpublish",
            "isAll"                 : isAll,
            "courseSettingId"       : component.get('v.recordId')
            
        });
        action.setCallback(this, function (response) {
            var state = response.getState();
            console.log("response",(JSON.stringify(response)),state);
            if (state === "SUCCESS") {
                let result = response.getReturnValue()
                console.log("result",(JSON.stringify(result)));
                $A.get("e.force:closeQuickAction").fire();
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "SUCCESS",
                    "message": "SUCCESS",
                    "type": "success"
                }).fire();
            } else {
                var toastEvt = $A.get("e.force:showToast");
                toastEvt.setParams({
                    "title": "Error",
                    "message": response.getError()[0].pageErrors[0].message,
                    "type": "error"
                }).fire();
            }
         });
         $A.enqueueAction(action)
    },
    selectall : function(component, event, helper) {
        if(component.get('v.selectall')){
            let _TrainingList = component.get('v.TrainingList')
            _TrainingList.forEach(item => {
                if(item.arrangementStatus == "Launched"){
                    item.onselect = true
                }
            });
            component.set('v.TrainingList',_TrainingList)
        }else{            
            let _TrainingList = component.get('v.TrainingList')
            _TrainingList.forEach(item => {
                if(item.arrangementStatus == "Launched"){
                    item.onselect = false
                }
            });
            component.set('v.TrainingList',_TrainingList)
        }
    },
    Cancel : function(component, event, helper) {
        $A.get("e.force:closeQuickAction").fire();
    }
})